<?php
/*
Plugin Name: Formidable Review Voting
Description: Reviewer selection with smart category filter, pagination, and leaderboard for Formidable entries.
Version: 1.3.8
Author: ChatGPT
*/

if (!defined('ABSPATH')) exit;

class FRV_Plugin {
    const TABLE = 'frv_votes';

    public static function init() {
        register_activation_hook(__FILE__, [__CLASS__, 'activate']);
        add_action('init', [__CLASS__, 'maybe_add_caps']);
        add_action('rest_api_init', [__CLASS__, 'register_routes']);
        add_shortcode('ff_review_voting', [__CLASS__, 'shortcode_voting']);
        add_shortcode('ff_review_leaderboard', [__CLASS__, 'shortcode_leaderboard']);
        add_shortcode('frv_whoami', [__CLASS__, 'shortcode_whoami']);
        add_shortcode('frv_field_dump', [__CLASS__, 'shortcode_field_dump']);
        add_action('wp_enqueue_scripts', [__CLASS__, 'register_assets']);
    }

    public static function activate() {
        self::maybe_create_table();
        add_role('ff_reviewer', '審核人員 (Reviewer)', ['read' => true, 'ff_review' => true]);
        $legacy = get_role('reviewer'); if ($legacy && !$legacy->has_cap('ff_review')) $legacy->add_cap('ff_review');
        $admin = get_role('administrator'); if ($admin && !$admin->has_cap('ff_review')) $admin->add_cap('ff_review');
    }

    protected static function table_name() { global $wpdb; return $wpdb->prefix . self::TABLE; }

    protected static function maybe_create_table() {
        global $wpdb;
        $table = self::table_name();
        $charset = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE IF NOT EXISTS $table (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            reviewer_id BIGINT UNSIGNED NOT NULL,
            submission_entry_id BIGINT UNSIGNED NOT NULL,
            created_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY uniq_vote (reviewer_id, submission_entry_id),
            KEY idx_submission (submission_entry_id),
            KEY idx_reviewer (reviewer_id)
        ) $charset;";
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }

    public static function maybe_add_caps() {
        foreach (['administrator','ff_reviewer','reviewer'] as $slug) {
            $role = get_role($slug);
            if ($role && !$role->has_cap('ff_review')) $role->add_cap('ff_review');
        }
    }

    public static function register_assets() {
        $v = '1.3.7';
        wp_register_script('frv-voting', plugins_url('assets/voting.js', __FILE__), [], $v, true);
        wp_register_style('frv-styles', plugins_url('assets/styles.css', __FILE__), [], $v);
    }

    public static function require_ff() { return class_exists('FrmEntry'); }
    public static function rest_permission($request) { return current_user_can('ff_review') || current_user_can('manage_options'); }

    public static function register_routes() {
        register_rest_route('ff/v1', '/vote', [
            'methods' => 'POST',
            'permission_callback' => [__CLASS__, 'rest_permission'],
            'args' => [
                'submission_entry_id' => ['required' => true, 'type' => 'integer'],
                'submissions_form_id' => ['required' => true, 'type' => 'integer'],
                'limit' => ['required' => true, 'type' => 'integer'],
            ],
            'callback' => [__CLASS__, 'rest_toggle_vote'],
        ]);
    }

    protected static function work_belongs_to_form($entry_id, $submissions_form_id) {
        global $wpdb;
        $form = $wpdb->get_var($wpdb->prepare(
            "SELECT form_id FROM {$wpdb->prefix}frm_items WHERE id=%d LIMIT 1",
            intval($entry_id)
        ));
        return intval($form) === intval($submissions_form_id);
    }

    protected static function count_user_votes($reviewer_id) {
        global $wpdb; $t = self::table_name();
        return intval($wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $t WHERE reviewer_id=%d", intval($reviewer_id))));
    }
    protected static function has_vote($reviewer_id, $submission_entry_id) {
        global $wpdb; $t = self::table_name();
        return intval($wpdb->get_var($wpdb->prepare("SELECT id FROM $t WHERE reviewer_id=%d AND submission_entry_id=%d LIMIT 1",
            intval($reviewer_id), intval($submission_entry_id))));
    }
    protected static function insert_vote($reviewer_id, $submission_entry_id) {
        global $wpdb; $t = self::table_name();
        $wpdb->insert($t, [
            'reviewer_id' => intval($reviewer_id),
            'submission_entry_id' => intval($submission_entry_id),
            'created_at' => current_time('mysql'),
        ]);
        return intval($wpdb->insert_id);
    }
    protected static function delete_vote($rid, $eid = null) {
        global $wpdb; $t = self::table_name();
        if ($eid === null) return (bool)$wpdb->delete($t, ['id' => intval($rid)]);
        return (bool)$wpdb->delete($t, ['reviewer_id' => intval($rid), 'submission_entry_id' => intval($eid)]);
    }

    public static function rest_toggle_vote($request) {
        $p = $request->get_params();
        $uid = get_current_user_id();
        if (!$uid) return new WP_Error('frv_login', 'login required', ['status' => 401]);
        $eid = intval($p['submission_entry_id']);
        $fid = intval($p['submissions_form_id']);
        $limit = max(1, intval($p['limit']));

        if (!self::work_belongs_to_form($eid, $fid)) return new WP_Error('frv_invalid_work', 'invalid submission', ['status' => 400]);

        $existing = self::has_vote($uid, $eid);
        if ($existing) {
            self::delete_vote($uid, $eid);
            $count = self::count_user_votes($uid);
            return ['status' => 'removed', 'count' => $count, 'left' => max(0, $limit - $count)];
        } else {
            $count = self::count_user_votes($uid);
            if ($count >= $limit) return new WP_Error('frv_limit', 'limit reached', ['status' => 400]);
            self::insert_vote($uid, $eid);
            $count = self::count_user_votes($uid);
            return ['status' => 'added', 'count' => $count, 'left' => max(0, $limit - $count)];
        }
    }

    protected static function get_entry_user($entry_id) {
        global $wpdb;
        $row = $wpdb->get_row($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->prefix}frm_items WHERE id=%d LIMIT 1", intval($entry_id)
        ));
        if (!$row) return [0, ''];
        $uid = intval($row->user_id);
        $u = $uid ? get_user_by('id', $uid) : null;
        return [$uid, $u ? $u->display_name : ''];
    }

    protected static function get_entry_meta_raw($entry_id, $field_id) {
        global $wpdb;
        return $wpdb->get_var($wpdb->prepare(
            "SELECT meta_value FROM {$wpdb->prefix}frm_item_metas WHERE item_id=%d AND field_id=%d ORDER BY id DESC LIMIT 1",
            intval($entry_id), intval($field_id)
        ));
    }

    protected static function path_to_url($path) {
        $path = trim((string)$path);
        if ($path === '') return '';
        if (preg_match('~^https?://~i', $path)) return esc_url_raw($path);
        $uploads = wp_upload_dir();
        $basedir = wp_normalize_path($uploads['basedir']);
        $baseurl = $uploads['baseurl'];
        $npath = wp_normalize_path($path);
        if (strpos($npath, $basedir) === 0) {
            $rel = ltrim(substr($npath, strlen($basedir)), '/');
            return esc_url_raw(rtrim($baseurl, '/') . '/' . $rel);
        }
        if (!preg_match('~^/|^https?://~i', $path)) {
            return esc_url_raw(rtrim($baseurl, '/') . '/' . ltrim($path, '/'));
        }
        return esc_url_raw(site_url($path));
    }

    protected static function extract_file_urls($raw) {
        $urls = [];
        $push = function($u) use (&$urls) { $u = is_string($u) ? trim($u) : ''; if ($u !== '') $urls[] = self::path_to_url($u); };
        $push_id = function($id) use (&$urls) { $id = intval($id); if ($id>0){ $u = wp_get_attachment_url($id); if ($u) $urls[] = $u; } };

        if (is_numeric($raw)) { $push_id($raw); return array_values(array_unique($urls)); }

        if (is_array($raw)) {
            foreach ($raw as $it) {
                if (is_string($it)) {
                    $it = trim($it); if ($it==='') continue;
                    if (is_numeric($it)) { $push_id($it); continue; }
                    if (strpos($it,'|')!==false || strpos($it,',')!==false || strpos($it,"
")!==false) {
                        $parts = preg_split('~[|,
]+~', $it);
                        foreach ($parts as $p) { $push($p); }
                        continue;
                    }
                    $push($it);
                } elseif (is_array($it)) {
                    $k = array_change_key_case($it, CASE_LOWER);
                    if (!empty($k['url'])) { $push($k['url']); continue; }
                    if (!empty($k['path'])) { $push($k['path']); continue; }
                    if (!empty($k['file'])) { $push($k['file']); continue; }
                    if (!empty($k['guid'])) { $push($k['guid']); continue; }
                    if (!empty($k['attachment_id'])) { $push_id($k['attachment_id']); continue; }
                    if (!empty($k['media_id'])) { $push_id($k['media_id']); continue; }
                    if (!empty($k['id'])) { $push_id($k['id']); continue; }
                    if (!empty($k['attachment'])) { $push_id($k['attachment']); continue; }
                    if (!empty($k['attachment_ids']) && is_array($k['attachment_ids'])) {
                        foreach ($k['attachment_ids'] as $_id) { $push_id($_id); }
                        continue;
                    }
                    if (count($it) === 1) { $sub = self::extract_file_urls(reset($it)); foreach ($sub as $su) $urls[] = $su; continue; }
                    foreach ($it as $v) { $sub = self::extract_file_urls($v); foreach ($sub as $su) $urls[] = $su; }
                } elseif (is_int($it) || is_numeric($it)) {
                    $push_id($it);
                }
            }
            return array_values(array_filter(array_unique($urls)));
        }

        if (is_string($raw)) {
            $raw = trim($raw);
            if ($raw === '') return [];
            if (function_exists('maybe_unserialize')) {
                $maybe = maybe_unserialize($raw);
                if (is_array($maybe)) return self::extract_file_urls($maybe);
            }
            $json = json_decode($raw, true);
            if (is_array($json)) return self::extract_file_urls($json);
            if (strpos($raw,'|')!==false || strpos($raw,',')!==false || strpos($raw,"\n")!==false) {
                $parts = preg_split('~[|,\n]+~', $raw);
                $out = [];
                foreach ($parts as $p) {
                    $p = trim($p);
                    if ($p==='') continue;
                    if (is_numeric($p)) {
                        $u = wp_get_attachment_url(intval($p));
                        if ($u) $out[] = $u; else $out[] = self::path_to_url($p);
                    } else {
                        $out[] = self::path_to_url($p);
                    }
                }
                return array_values(array_filter(array_unique($out)));
            }
            return [ self::path_to_url($raw) ];
        }
        return [];
    }

    protected static function humanize_value($raw) {
        if (is_null($raw) || $raw === '') return '';
        if (is_array($raw)) {
            $keys = array_map('strtolower', array_keys($raw));
            if (isset($raw['first']) || isset($raw['last']) || in_array('first', $keys) || in_array('last', $keys)) {
                $first = isset($raw['first']) ? $raw['first'] : (isset($raw['First']) ? $raw['First'] : '');
                $last  = isset($raw['last'])  ? $raw['last']  : (isset($raw['Last'])  ? $raw['Last'] : '');
                $mid   = isset($raw['middle']) ? $raw['middle'] : (isset($raw['Middle']) ? $raw['Middle'] : '');
                $parts = array_filter([$first, $mid, $last], function($v){ return (string)$v !== ''; });
                return trim(implode(' ', $parts));
            }
            $flat = [];
            foreach ($raw as $v) $flat[] = is_array($v) ? self::humanize_value($v) : $v;
            $flat = array_filter($flat, function($v){ return (string)$v !== ''; });
            return implode(', ', $flat);
        }
        if (is_string($raw)) {
            $raw = trim($raw);
            if ($raw === '') return '';
            if (function_exists('maybe_unserialize')) {
                $maybe = maybe_unserialize($raw);
                if (is_array($maybe)) return self::humanize_value($maybe);
            }
            $json = json_decode($raw, true);
            if (is_array($json)) return self::humanize_value($json);
            return $raw;
        }
        return (string)$raw;
    }

    protected static function get_field_text($entry_id, $field_id) {
        $field_id = intval($field_id);
        if ($field_id <= 0) return '';
        $raw = self::get_entry_meta_raw($entry_id, $field_id);
        return esc_html(self::humanize_value($raw));
    }

    protected static function distinct_categories($form_id, $category_field_id) {
        global $wpdb;
        if ($category_field_id <= 0) return [];
        $rows = $wpdb->get_results($wpdb->prepare(
            "SELECT im.meta_value FROM {$wpdb->prefix}frm_item_metas im
             JOIN {$wpdb->prefix}frm_items i ON i.id = im.item_id
             WHERE i.form_id=%d AND im.field_id=%d",
            $form_id, $category_field_id
        ));
        $out = [];
        foreach ($rows as $r) {
            $raw = is_string($r->meta_value) ? $r->meta_value : (string)$r->meta_value;
            $disp = self::humanize_value($raw);
            if ($disp === '') continue;
            $key = md5($raw);
            $out[$key] = ['text' => $disp, 'raw' => $raw];
        }
        ksort($out);
        return $out;
    }

    protected static function query_entries_smart($form_id, $category_field_id, $category_key, $per_page, $page, &$debug = null) {
        global $wpdb;
        $debug_info = ['mode' => 'sql', 'total_sql' => 0, 'total_php' => 0, 'cat_key' => $category_key];
        $per_page = max(1, intval($per_page));
        $page = max(1, intval($page));
        $offset = ($page - 1) * $per_page;

        $params = [ $form_id ];
        $join = '';
        $where = ' WHERE i.form_id=%d ';
        if ($category_field_id > 0 && $category_key !== '') {
            $cats = self::distinct_categories($form_id, $category_field_id);
            if (isset($cats[$category_key])) {
                $raw = $cats[$category_key]['raw'];
                $join .= " JOIN {$wpdb->prefix}frm_item_metas imc ON imc.item_id=i.id AND imc.field_id=%d ";
                $where .= " AND imc.meta_value = %s ";
                $params[] = $category_field_id;
                $params[] = $raw;
                $debug_info['cat_text'] = self::humanize_value($raw);
            }
        }

        $sql_count = "SELECT COUNT(DISTINCT i.id) FROM {$wpdb->prefix}frm_items i $join $where";
        $total = intval($wpdb->get_var($wpdb->prepare($sql_count, $params)));
        $debug_info['total_sql'] = $total;

        if ($total > 0 || $category_key === '' || $category_field_id <= 0) {
            $sql_page = "SELECT DISTINCT i.id FROM {$wpdb->prefix}frm_items i $join $where ORDER BY i.id DESC LIMIT %d OFFSET %d";
            $params_page = array_merge($params, [ $per_page, $offset ]);
            $ids = $wpdb->get_col($wpdb->prepare($sql_page, $params_page));
            if (is_array($debug)) $debug = $debug_info;
            return [ array_map('intval', $ids), $total ];
        }

        $debug_info['mode'] = 'php';
        $ids_all = $wpdb->get_col($wpdb->prepare("SELECT id FROM {$wpdb->prefix}frm_items WHERE form_id=%d ORDER BY id DESC", $form_id));
        $cats = self::distinct_categories($form_id, $category_field_id);
        $needle = isset($cats[$category_key]) ? trim(self::humanize_value($cats[$category_key]['raw'])) : '';
        $matched = [];
        foreach ($ids_all as $eid) {
            $raw = self::get_entry_meta_raw(intval($eid), $category_field_id);
            $text = trim(self::humanize_value($raw));
            if ($needle === '' || $text === '') continue;
            if (mb_stripos($text, $needle) !== false) { $matched[] = intval($eid); }
        }
        $debug_info['total_php'] = count($matched);
        $total = count($matched);
        $ids = array_slice($matched, $offset, $per_page);
        if (is_array($debug)) $debug = $debug_info;
        return [ $ids, $total ];
    }

    public static function shortcode_voting($atts) {
        if (!is_user_logged_in()) return '<div>請先登入</div>';
        if (!current_user_can('ff_review') && !current_user_can('manage_options')) return '<div>權限不足</div>';
        if (!self::require_ff()) return '<div>需要安裝 Formidable Forms</div>';
        $a = shortcode_atts([
            'submissions_form_id' => 0,
            'image_field_ids' => '',
            'title_field_id' => 0,
            'limit' => 30,
            'reg_field_id' => 0,
            'category_field_id' => 0,
            'name_field_id' => 0,
            'per_page' => 9,
            'show_filter' => 1,
            'debug' => 0,
        ], $atts);
        $a_int = array_map('intval', $a);
        $img_fields = array_filter(array_map('intval', array_map('trim', explode(',', (string)$a['image_field_ids']))));
        if ($a_int['submissions_form_id']<=0 || empty($img_fields)) return '<div>參數不足：請提供 submissions_form_id 與 image_field_ids</div>';

        $current_cat = isset($_GET['frv_cat']) ? preg_replace('~[^a-f0-9]~i','', wp_unslash($_GET['frv_cat'])) : '';
        $current_page = isset($_GET['frv_page']) ? max(1, intval($_GET['frv_page'])) : 1;
        $current_pick = isset($_GET['frv_pick']) ? sanitize_text_field(wp_unslash($_GET['frv_pick'])) : '';

        $debug_info = [];
        list($entry_ids, $total) = self::query_entries_smart($a_int['submissions_form_id'], $a_int['category_field_id'], $current_cat, $a_int['per_page'], $current_page, $debug_info);
        $total_pages = max(1, ceil($total / max(1,$a_int['per_page'])));

        $user_id = get_current_user_id();
        $table = self::table_name();
        global $wpdb;
        $selected_rows = $wpdb->get_results($wpdb->prepare("SELECT submission_entry_id FROM $table WHERE reviewer_id=%d", $user_id));
        $selected = [];
        foreach ($selected_rows as $r) $selected[intval($r->submission_entry_id)] = true;
        $selected_count = count($selected);

        // Apply picked/unpicked filter
        if (in_array($current_pick, ['picked','unpicked'], true)) {
            list($ids_all, $total_src) = self::query_entries_smart($a_int['submissions_form_id'], $a_int['category_field_id'], $current_cat, 1000000, 1, $debug_info);
            $filtered = [];
            foreach ($ids_all as $_id) {
                $isPicked = isset($selected[intval($_id)]);
                if (($current_pick==='picked' && $isPicked) || ($current_pick==='unpicked' && !$isPicked)) {
                    $filtered[] = intval($_id);
                }
            }
            $total = count($filtered);
            $total_pages = max(1, ceil($total / max(1,$a_int['per_page'])));
            $offset = ($current_page-1) * max(1,$a_int['per_page']);
            $entry_ids = array_slice($filtered, $offset, max(1,$a_int['per_page']));
        }

        wp_enqueue_style('frv-styles');
        wp_enqueue_script('frv-voting');
        wp_localize_script('frv-voting', 'FRV_DATA', [
            'rest' => esc_url_raw(rest_url('ff/v1/vote')),
            'nonce' => wp_create_nonce('wp_rest'),
            'submissions_form_id' => $a_int['submissions_form_id'],
            'limit' => $a_int['limit'],
            'i18n' => ['add' => '勾選', 'remove' => '取消', 'limit' => '已達上限'],
            'filters' => ['catParam' => 'frv_cat', 'pageParam' => 'frv_page', 'pickParam' => 'frv_pick']
        ]);

        $cats = $a_int['show_filter'] ? self::distinct_categories($a_int['submissions_form_id'], $a_int['category_field_id']) : [];
        $base_url = remove_query_arg(['frv_cat','frv_page','frv_pick']);

        ob_start();
        echo '<div class="frv-wrap">';
        echo '<div class="frv-top">';
        if ($a_int['show_filter'] && !empty($cats)) {
            echo '<div class="frv-filter">';
            echo '<label>參加類別：</label>';
            echo '<select class="frv-select frv-select-cat" data-base="'.esc_url($base_url).'">';
            $sel = $current_cat === '' ? ' selected' : '';
            echo '<option value=""'.$sel.'>全部</option>';
            foreach ($cats as $key => $info) {
                $sel = ($key === $current_cat) ? ' selected' : '';
                echo '<option value="'.esc_attr($key).'"'.$sel.'>'.esc_html($info['text']).'</option>';
            }
            echo '</select>';
            echo ' <label style="margin-left:12px">顯示：</label>';
            echo '<select class="frv-select frv-select-pick" data-base="'.esc_url($base_url).'">';
            $opt_all = ($current_pick==='') ? ' selected' : '';
            $opt_p = ($current_pick==='picked') ? ' selected' : '';
            $opt_u = ($current_pick==='unpicked') ? ' selected' : '';
            echo '<option value=""'.$opt_all.'>全部</option>';
            echo '<option value="picked"'.$opt_p.'>已勾選</option>';
            echo '<option value="unpicked"'.$opt_u.'>未勾選</option>';
            echo '</select>';
            echo '</div>';
        } else {
            echo '<div></div>';
        }
        $pct = min(100, max(0, ($a_int['limit']>0) ? round($selected_count/$a_int['limit']*100) : 0));
        echo '<div class="frv-counter"><span class="frv-count-text">已選 <span class="frv-count">'.intval($selected_count).'</span> / '.intval($a_int['limit']).'</span><span class="frv-bar"><span class="frv-bar-inner" style="width:'.$pct.'%"></span></span></div>';
        echo '</div>';

        echo '<ul class="frv-list">';
        foreach ($entry_ids as $eid) {
            $eid = intval($eid);
            list($member_id, $member_name) = self::get_entry_user($eid);

            $reg = $a_int['reg_field_id'] > 0 ? self::get_field_text($eid, $a_int['reg_field_id']) : '';
            $reg = $reg !== '' ? $reg : ('#' . $eid);
            $cat = $a_int['category_field_id'] > 0 ? self::get_field_text($eid, $a_int['category_field_id']) : '';
            $name = $a_int['name_field_id'] > 0 ? self::get_field_text($eid, $a_int['name_field_id']) : $member_name;

            $urls = [];
            foreach ($img_fields as $fid) {
                $raw = self::get_entry_meta_raw($eid, $fid);
                $files = self::extract_file_urls($raw);
                foreach ($files as $u) { if ($u) $urls[] = esc_url($u); }
            }
            $urls = array_values(array_unique(array_filter($urls)));

            $active = isset($selected[$eid]);
            $btn = $active ? '取消' : '勾選';
            $cls = $active ? 'active' : '';

            echo '<li class="frv-item" data-entry-id="'.$eid.'">';
            echo '<div class="frv-meta"><div class="frv-member"><b>報名編號：</b>'.esc_html($reg).'　<b>參加類別：</b>'.esc_html($cat).'　<b>姓名：</b>'.esc_html($name).'</div></div>';
            echo '<div class="frv-images">';
            $shown = 0;
            foreach ($urls as $u) {
                echo '<a href="'.$u.'" target="_blank" rel="noopener noreferrer"><img src="'.$u.'" loading="lazy" alt="" /></a>';
                $shown++; if ($shown>=9) break;
            }
            echo '</div>';
            echo '<div class="frv-actions"><button type="button" class="frv-btn '.$cls.'" data-entry-id="'.$eid.'">'.$btn.'</button></div>';
            echo '</li>';
        }
        echo '</ul>';

        if ($total_pages > 1) {
            echo '<div class="frv-pagination">';
            $make_link = function($p) use ($base_url, $current_cat, $current_pick) {
                $args = [];
                if ($current_cat !== '') $args['frv_cat'] = $current_cat;
                if ($current_pick !== '') $args['frv_pick'] = $current_pick;
                $args['frv_page'] = max(1, intval($p));
                return esc_url(add_query_arg($args, $base_url));
            };
            $prev_disabled = ($current_page <= 1) ? ' disabled' : '';
            $next_disabled = ($current_page >= $total_pages) ? ' disabled' : '';
            echo '<a class="frv-pg frv-prev'.$prev_disabled.'" href="'.($current_page>1?$make_link($current_page-1):'#').'"'.($current_page<=1?' aria-disabled="true"':'').'>上一頁</a>';
            $start = max(1, $current_page - 2);
            $end = min($total_pages, $current_page + 2);
            if ($start > 1) { echo '<a class="frv-pg" href="'.$make_link(1).'">1</a>'; if ($start>2) echo '<span class="frv-ellipsis">…</span>'; }
            for ($p=$start; $p<=$end; $p++) {
                $cls = ($p==$current_page) ? ' frv-current' : '';
                echo '<a class="frv-pg'.$cls.'" href="'.$make_link($p).'">'.$p.'</a>';
            }
            if ($end < $total_pages) { if ($end<$total_pages-1) echo '<span class="frv-ellipsis">…</span>'; echo '<a class="frv-pg" href="'.$make_link($total_pages).'">'.$total_pages.'</a>'; }
            echo '<a class="frv-pg frv-next'.$next_disabled.'" href="'.($current_page<$total_pages?$make_link($current_page+1):'#').'"'.($current_page>=$total_pages?' aria-disabled="true"':'').'>下一頁</a>';
            echo '</div>';
        }

        if ($a_int['debug']) {
            echo '<pre class="frv-debug" style="background:#f9fafb;border:1px solid #eee;padding:8px;border-radius:8px;">';
            echo esc_html(print_r($debug_info, true));
            echo '</pre>';
        }

        echo '</div>';
        return ob_get_clean();
    }

    public static function shortcode_leaderboard($atts) {
        if (!is_user_logged_in()) return '<div>請先登入</div>';
        if (!current_user_can('manage_options')) return '<div>只有管理員可見</div>';
        if (!self::require_ff()) return '<div>需要安裝 Formidable Forms</div>';
        $a = shortcode_atts([ 'submissions_form_id' => 0, 'title_field_id' => 0 ], $atts);
        $a['submissions_form_id'] = intval($a['submissions_form_id']);
        $a['title_field_id'] = intval($a['title_field_id']);
        if ($a['submissions_form_id']<=0) return '<div>參數不足：請提供 submissions_form_id</div>';

        global $wpdb;
        $t = self::table_name();
        $rows = $wpdb->get_results("SELECT submission_entry_id, COUNT(*) AS votes FROM $t GROUP BY submission_entry_id");
        $work_votes = [];
        foreach ($rows as $r) $work_votes[intval($r->submission_entry_id)] = intval($r->votes);
        arsort($work_votes);
        $rank = 1;

        ob_start();
        echo '<div class="frv-leaderboard">';
        echo '<h3>排行榜</h3>';
        echo '<table class="frv-table"><thead><tr><th>#</th><th>會員</th><th>作品EntryID</th><th>票數</th></tr></thead><tbody>';
        foreach ($work_votes as $eid => $count) {
            list($member_id, $member_name) = self::get_entry_user($eid);
            echo '<tr><td>'.($rank++).'</td><td>'.esc_html($member_name).' (ID '.$member_id.')</td><td>'.$eid.'</td><td>'.$count.'</td></tr>';
        }
        echo '</tbody></table>';
        echo '</div>';
        return ob_get_clean();
    }

    public static function shortcode_whoami($atts) {
        if (!is_user_logged_in()) return '<div>未登入</div>';
        $u = wp_get_current_user();
        $roles = implode(', ', (array)$u->roles);
        $has = current_user_can('ff_review') ? 'YES' : 'NO';
        return '<div>使用者：'.esc_html($u->user_login).'｜角色：'.esc_html($roles).'｜ff_review 能力：<b>'.$has.'</b></div>';
    }

    public static function shortcode_field_dump($atts){
        if (!current_user_can('manage_options')) return '<div>只有管理員可用</div>';
        $a = shortcode_atts(['entry_id'=>0,'fields'=>''], $atts);
        $eid = intval($a['entry_id']);
        $field_ids = array_filter(array_map('intval', array_map('trim', explode(',', (string)$a['fields']))));
        if ($eid<=0 || empty($field_ids)) return '<div>請提供 entry_id 與 fields</div>';
        ob_start();
        echo '<div class="frv-dump"><h4>Entry #'.intval($eid).'</h4><pre style="white-space:pre-wrap;background:#f8fafc;border:1px solid #e5e7eb;padding:8px;border-radius:8px">';
        foreach ($field_ids as $fid){
            $raw = self::get_entry_meta_raw($eid, $fid);
            $urls = self::extract_file_urls($raw);
            echo 'Field '.$fid.': ' . esc_html(print_r($raw, true)) . "\n";
            echo ' → URLs: ' . esc_html(print_r($urls, true)) . "\n\n";
        }
        echo '</pre></div>';
        return ob_get_clean();
    }
}

FRV_Plugin::init();
