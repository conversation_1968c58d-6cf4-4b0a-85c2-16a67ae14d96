(function(){
function q(a){return document.querySelector(a)}
function qa(a){return Array.prototype.slice.call(document.querySelectorAll(a))}
function post(url, data, nonce){
  return fetch(url, {
    method: 'POST',
    headers: {'Content-Type':'application/json','X-WP-Nonce': nonce},
    body: JSON.stringify(data),
    credentials: 'same-origin'
  }).then(r=>{
    if(!r.ok){return r.json().then(j=>Promise.reject(j))}
    return r.json()
  })
}
function updateProgress(){
  var countEl = q('.frv-count'); if(!countEl) return;
  var barInner = q('.frv-bar-inner'); if(!barInner) return;
  var limit = parseInt((window.FRV_DATA && FRV_DATA.limit) || '0',10);
  var count = parseInt(countEl.textContent||'0',10);
  var pct = limit>0 ? Math.min(100, Math.round(count/limit*100)) : 0;
  barInner.style.width = pct + '%';
}
function init(){
  if (!window.FRV_DATA) return;
  var wrap = q('.frv-wrap'); if(!wrap) return;

  qa('.frv-images img').forEach(function(img){
    img.addEventListener('error', function(){
      var a = img.parentNode;
      if (!a) return;
      a.classList.add('frv-file');
      a.textContent = '檔案';
      if (img && img.parentNode) img.parentNode.removeChild(img);
    });
  });

  qa('.frv-select').forEach(function(sel){
    sel.addEventListener('change', function(){
      var base = sel.getAttribute('data-base') || window.location.href;
      var params = new URLSearchParams(window.location.search);
      params.delete(FRV_DATA.filters.pageParam);
      var catSel = q('.frv-select-cat');
      params.delete(FRV_DATA.filters.catParam);
      if (catSel && catSel.value) params.set(FRV_DATA.filters.catParam, catSel.value);
      var pickSel = q('.frv-select-pick');
      params.delete(FRV_DATA.filters.pickParam);
      if (pickSel && pickSel.value) params.set(FRV_DATA.filters.pickParam, pickSel.value);
      params.set(FRV_DATA.filters.pageParam, '1');
      var url = base + (params.toString() ? ('?' + params.toString()) : '');
      window.location.assign(url);
    });
  });

  var countEl = q('.frv-count');
  qa('.frv-btn').forEach(function(btn){
    btn.addEventListener('click', function(){
      var entryId = parseInt(btn.getAttribute('data-entry-id')||'0',10);
      var isActive = btn.classList.contains('active');
      var limit = parseInt((FRV_DATA && FRV_DATA.limit) || '0',10);
      var current = parseInt(countEl.textContent||'0',10);
      if(!isActive && current >= limit){ alert((FRV_DATA && FRV_DATA.i18n && FRV_DATA.i18n.limit) || 'Limit'); return; }
      btn.disabled = true;
      post(FRV_DATA.rest, {
        submission_entry_id: entryId,
        submissions_form_id: parseInt(FRV_DATA.submissions_form_id),
        limit: limit
      }, FRV_DATA.nonce).then(function(res){
        if(res.status==='added'){
          btn.classList.add('active'); btn.textContent = (FRV_DATA && FRV_DATA.i18n && FRV_DATA.i18n.remove) || '取消';
        }else if(res.status==='removed'){
          btn.classList.remove('active'); btn.textContent = (FRV_DATA && FRV_DATA.i18n && FRV_DATA.i18n.add) || '勾選';
        }
        if(typeof res.count !== 'undefined'){ countEl.textContent = String(res.count); updateProgress(); }
      }).catch(function(err){
        alert((err && err.message) ? err.message : 'Error');
      }).finally(function(){ btn.disabled = false; });
    })
  })

  updateProgress();
}
document.addEventListener('DOMContentLoaded', init);
})();